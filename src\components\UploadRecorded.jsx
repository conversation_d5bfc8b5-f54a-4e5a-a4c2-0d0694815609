import React, { useState, useContext, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { PlatformContext } from '../App';
import { platform } from '../services/platformService';
import Sidebar from './Sidebar';
import FacultyAttendanceSelector from './FacultyAttendanceSelector';
import DatePicker from './DatePicker';
import { getMediaDuration, formatDuration, estimateProcessingTime } from '../utils/mediaUtils';
import { saveTranscript } from '../utils/transcriptStorage';
import backIcon from '../assets/back.png';
import uploadIcon from '../assets/upload_recorded.png';

const VALID_TYPES = {
  // Video formats
  'mp4': 'video/mp4',
  'webm': 'video/webm',
  'mkv': 'video/x-matroska',
  'mov': 'video/quicktime',
  'avi': 'video/x-msvideo',
  // Audio formats
  'mp3': 'audio/mpeg',
  'wav': 'audio/wav',
  'm4a': 'audio/x-m4a',
  'flac': 'audio/flac',
  'aac': 'audio/aac',
  'ogg': 'audio/ogg',
  // Text formats
  'txt': 'text/plain',
  'md': 'text/markdown'
};

const MAX_FILE_SIZE = 5 * 1024 * 1024 * 1024; // 5GB

const validateFile = (file) => {
  const extension = file.name.split('.').pop().toLowerCase();
  const expectedType = VALID_TYPES[extension];

  // Special handling for text files which might have varying MIME types
  const isTextFile = ['txt', 'md'].includes(extension);

  // For text files, we're more lenient with the MIME type check
  if (isTextFile) {
    if (file.type.startsWith('text/') || file.type === 'application/octet-stream') {
      // Valid text file
    } else {
      return {
        isValid: false,
        error: 'The selected file does not appear to be a valid text file'
      };
    }
  } else if (!expectedType || file.type !== expectedType) {
    // For non-text files, strict MIME type checking
    return {
      isValid: false,
      error: 'Please select a valid video, audio, or text file'
    };
  }

  if (file.size > MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: 'File size must be less than 5GB'
    };
  }

  return { isValid: true, isTextFile: isTextFile };
};

const ProcessingIndicator = ({ progress, stage, status, file, details, duration, estimatedTime, iteration, totalIterations }) => {
  // Get file information if available
  const fileInfo = file ? {
    name: file.name,
    size: (file.size / (1024 * 1024)).toFixed(2) + ' MB',
    type: file.type,
    extension: file.name.split('.').pop().toLowerCase(),
    duration: duration ? formatDuration(duration) : 'Unknown',
    estimatedTime: estimatedTime ? estimatedTime.description : 'Unknown'
  } : null;

  // Iteration information hidden as requested
  const iterationInfo = null;

  // Determine file type
  const isVideo = fileInfo ?
    ['mp4', 'webm', 'mkv', 'mov', 'avi'].includes(fileInfo.extension) : false;
  const isTextFile = fileInfo ?
    ['txt', 'md'].includes(fileInfo.extension) : false;

  // Process stage information
  const stageInfo = {
    'Converting': { color: '#8e44ad', icon: '🔄' },
    'Initializing': { color: '#3498db', icon: '🚀' },
    'Transcribing': { color: '#2ecc71', icon: '🎙️' },
    'Generating Minutes': { color: '#e67e22', icon: '📝' },
    'Uploading': { color: '#f39c12', icon: '📤' },
    'Processing': { color: '#1abc9c', icon: '⚙️' },
    'Completed': { color: '#27ae60', icon: '✅' },
    'Error': { color: '#e74c3c', icon: '❌' }
  };

  // Get current stage info
  const currentStage = stageInfo[stage] || { color: '#034FAF', icon: '⏳' };

  return (
    <div style={{
      marginTop: '20px',
      padding: '20px',
      borderRadius: '12px',
      backgroundColor: 'rgba(255, 255, 255, 0.15)',
      color: '#034FAF',
      textAlign: 'left',
      boxShadow: '0 4px 15px rgba(0, 0, 0, 0.05)',
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.25)',
      maxWidth: '800px',
      width: '100%',
    }}>
      <div style={{
        fontWeight: 'bold',
        fontSize: '18px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: '15px'
      }}>
        <span style={{
          display: 'inline-block',
          marginRight: '10px',
          fontSize: '24px'
        }}>{currentStage.icon}</span>
        {status}
      </div>

      {/* File information */}
      {fileInfo && (
        <div style={{
          fontSize: '14px',
          marginTop: '8px',
          padding: '12px',
          backgroundColor: 'rgba(255, 255, 255, 0.5)',
          borderRadius: '8px',
          border: '1px solid rgba(255, 255, 255, 0.3)',
        }}>
          <div><strong>File:</strong> {fileInfo.name}</div>
          <div><strong>Size:</strong> {fileInfo.size}</div>
          <div><strong>Type:</strong> {isTextFile ? 'Text' : isVideo ? 'Video' : 'Audio'}</div>
          {!isTextFile && <div><strong>Duration:</strong> {fileInfo.duration}</div>}
          {isTextFile && <div><strong>Language:</strong> {file.textLanguage === 'english' ? 'English' : 'Tagalog'}</div>}
          <div><strong>Est. Processing Time:</strong> {fileInfo.estimatedTime}</div>
        </div>
      )}

      {/* Processing stages */}
      <div style={{
        marginTop: '20px',
        padding: '15px',
        backgroundColor: 'rgba(255, 255, 255, 0.5)',
        borderRadius: '8px',
        border: '1px solid rgba(255, 255, 255, 0.3)',
      }}>
        <div style={{ fontWeight: 'bold', marginBottom: '10px' }}>Processing Pipeline:</div>

        {Object.keys(stageInfo).map((stageName, index) => {
          const isCurrentStage = stageName === stage;
          const isPastStage = (
            (stageName === 'Converting' && ['Initializing', 'Transcribing', 'Generating Minutes', 'Completed'].includes(stage)) ||
            (stageName === 'Initializing' && ['Transcribing', 'Generating Minutes', 'Completed'].includes(stage)) ||
            (stageName === 'Transcribing' && ['Generating Minutes', 'Completed'].includes(stage)) ||
            (stageName === 'Generating Minutes' && ['Completed'].includes(stage))
          );

          if (['Converting', 'Initializing', 'Transcribing', 'Generating Minutes', 'Completed'].includes(stageName)) {
            return (
              <div key={stageName} style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: '8px',
                padding: '8px 12px',
                borderRadius: '6px',
                backgroundColor: isCurrentStage ? `${stageInfo[stageName].color}20` : 'transparent',
                border: isCurrentStage ? `1px solid ${stageInfo[stageName].color}40` : 'none',
                opacity: isCurrentStage || isPastStage ? 1 : 0.5,
              }}>
                <span style={{
                  display: 'inline-block',
                  width: '24px',
                  height: '24px',
                  borderRadius: '50%',
                  backgroundColor: isCurrentStage || isPastStage ? stageInfo[stageName].color : '#ccc',
                  color: 'white',
                  textAlign: 'center',
                  lineHeight: '24px',
                  marginRight: '10px',
                  fontSize: '12px',
                  fontWeight: 'bold'
                }}>
                  {isPastStage ? '✓' : isCurrentStage ? stageInfo[stageName].icon : index + 1}
                </span>
                <span style={{
                  fontWeight: isCurrentStage ? 'bold' : 'normal',
                  color: isCurrentStage ? stageInfo[stageName].color : isPastStage ? '#555' : '#999'
                }}>
                  {stageName}
                </span>
                {isCurrentStage && (
                  <span style={{
                    marginLeft: 'auto',
                    backgroundColor: stageInfo[stageName].color,
                    color: 'white',
                    padding: '2px 8px',
                    borderRadius: '10px',
                    fontSize: '12px'
                  }}>
                    In Progress
                  </span>
                )}
                {isPastStage && (
                  <span style={{
                    marginLeft: 'auto',
                    color: '#27ae60',
                    fontSize: '12px'
                  }}>
                    Completed
                  </span>
                )}
              </div>
            );
          }
          return null;
        })}
      </div>

      {/* Current stage details */}
      {stage && (
        <div style={{
          marginTop: '15px',
          padding: '12px',
          backgroundColor: `${currentStage.color}10`,
          borderRadius: '8px',
          border: `1px solid ${currentStage.color}30`,
          color: currentStage.color,
          fontWeight: '500'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ marginRight: '8px' }}>{currentStage.icon}</span>
            <span>Current Stage: <strong>{stage}</strong></span>
          </div>
          <div style={{
              marginTop: '8px',
              fontSize: '14px',
              padding: '8px',
              backgroundColor: 'rgba(255, 255, 255, 0.5)',
              borderRadius: '6px'
            }}>
              {/* Iteration info hidden as requested */}

              {/* Show details if available, ensuring it matches the current stage */}
              <div>
                {details && details.startsWith("In progress: Generating") && stage === "Transcribing" ?
                  "In progress: Transcribing audio..." :
                  details}
              </div>
            </div>
        </div>
      )}

      {/* Progress bar */}
      <div style={{
        width: '100%',
        height: '8px',
        backgroundColor: 'rgba(3, 79, 175, 0.1)',
        borderRadius: '4px',
        marginTop: '20px',
        overflow: 'hidden'
      }}>
        <div style={{
          width: `${progress}%`,
          height: '100%',
          backgroundColor: currentStage.color || '#034FAF',
          borderRadius: '4px',
          transition: 'width 0.3s ease'
        }} />
      </div>

      {/* Progress percentage */}
      <div style={{
        fontSize: '14px',
        marginTop: '8px',
        textAlign: 'center',
        fontWeight: '500',
        color: currentStage.color || '#034FAF'
      }}>
        {Math.round(progress)}% Complete
      </div>
    </div>
  );
};

const UploadRecorded = () => {
  const platformType = useContext(PlatformContext);
  const navigate = useNavigate();

  const [file, setFile] = useState(null);
  const [error, setError] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [uploadResponse, setUploadResponse] = useState(null);
  const [processingStatus, setProcessingStatus] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [processingStage, setProcessingStage] = useState('');
  const [processingDetails, setProcessingDetails] = useState('');
  const [processingIteration, setProcessingIteration] = useState(null);
  const [processingTotalIterations, setProcessingTotalIterations] = useState(null);
  const [isServerAvailable, setIsServerAvailable] = useState(false);
  const [title, setTitle] = useState('');
  const [meetingDate, setMeetingDate] = useState(new Date());
  const [facultyAttendance, setFacultyAttendance] = useState([]);
  const [mediaDuration, setMediaDuration] = useState(null);
  const [processingEstimate, setProcessingEstimate] = useState(null);
  const [isTextFile, setIsTextFile] = useState(false);
  const [textLanguage, setTextLanguage] = useState('tagalog'); // 'tagalog' or 'english'

  // Memoize the attendance change handler to prevent infinite re-renders
  const handleAttendanceChange = useCallback((attendanceData) => {
    setFacultyAttendance(attendanceData);
  }, []);

  // Combine both server checks into one useEffect
  useEffect(() => {
    const checkServerConnection = async () => {
      try {
        const isAvailable = await platform.isServerAvailable();
        if (!isAvailable) {
          setError('Server connection failed. Please check if the server is running.');
          return;
        }
        console.log('Server connection successful');
      } catch (error) {
        setError(`Connection error: ${error.message}`);
        console.error('Server connection error:', error);
      }
    };

    checkServerConnection();
  }, []); // Removed dependency array to run only once

  const handleFileSelection = async (selectedFile) => {
    if (!selectedFile) {
      setFile(null);
      setError('');
      setMediaDuration(null);
      setProcessingEstimate(null);
      setIsTextFile(false);
      return;
    }

    const validation = validateFile(selectedFile);
    if (validation.isValid) {
      setFile(selectedFile);
      setError('');
      setIsTextFile(validation.isTextFile || false);

      try {
        // Get media duration for audio/video files
        if (!validation.isTextFile) {
          const duration = await getMediaDuration(selectedFile);
          setMediaDuration(duration);

          // Calculate estimated processing time
          const estimate = estimateProcessingTime(duration);
          setProcessingEstimate(estimate);
        } else {
          // For text files, set duration and estimate to null
          setMediaDuration(null);
          // Set a fixed processing estimate for text files
          setProcessingEstimate({
            description: "5-10 minutes (text file)",
            minMinutes: 5,
            maxMinutes: 10
          });
        }
      } catch (err) {
        console.error('Error getting media duration:', err);
        setMediaDuration(null);
        setProcessingEstimate(null);
      }
    } else {
      setFile(null);
      setError(validation.error);
      setMediaDuration(null);
      setProcessingEstimate(null);
      setIsTextFile(false);
    }
  };

  const handleFileChange = (event) => {
    handleFileSelection(event.target.files[0]);
  };

  const handleDragEnter = (event) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (event) => {
    event.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (event) => {
    event.preventDefault();
    setIsDragging(false);
    handleFileSelection(event.dataTransfer.files[0]);
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const openFileDialog = () => {
    document.getElementById('fileInput').click();
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleUpload = async (file) => {
    try {
      setIsProcessing(true);
      setError('');
      setUploadResponse(null);
      setProcessingStatus('Starting upload...');
      setUploadProgress(0);

      // Validate file
      if (!file) {
        throw new Error('No file selected');
      }

      // Check file type
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (!VALID_TYPES[fileExtension]) {
        throw new Error(`Unsupported file type: ${fileExtension}`);
      }

      // Get user ID from localStorage
      const userId = localStorage.getItem('userId');

      // Validate title
      if (!title.trim()) {
        throw new Error('Please enter a title for this minute');
      }

      const result = await platform.processAudio(file, {
        onProgress: (progress) => {
          setUploadProgress(progress);
          setProcessingStatus("Processing: " + Math.round(progress) + "%");
        },
        onStage: (stage, details, iterationInfo) => {
          setProcessingStage(stage);

          // Ensure details match the current stage
          if (details) {
            // If we're in Transcribing stage but details mention "Generating minutes",
            // replace with a transcribing message
            if (stage === "Transcribing" && details.includes("Generating meeting minutes")) {
              setProcessingDetails("In progress: Transcribing audio...");
            } else {
              setProcessingDetails(details);
            }
          }

          // Don't display iteration information as requested
          setProcessingIteration(null);
          setProcessingTotalIterations(null);
        },
        metadata: {
          userId,
          title,
          meetingDate: meetingDate.toISOString(),
          facultyAttendance: JSON.stringify(facultyAttendance),
          mediaDuration: mediaDuration,
          isTextFile: isTextFile,
          textLanguage: textLanguage
        }
      });

      if (!result) {
        throw new Error('No response from server');
      }

      setProcessingStatus('Processing complete!');

      // Log the result for debugging
      console.log('Processing result:', JSON.stringify(result, null, 2));

      // Store the response
      setUploadResponse({
        status: 'success',
        processingResult: {
          transcript_file: result.result.data.transcript_file,
          minutes_file: result.result.data.minutes_file,
          transcript: result.result.data.transcript,
          minutes: result.result.data.minutes,
          speakers: result.result.data.speakers,
          transcription_dir: result.result.data.transcription_dir,
          minutes_dir: result.result.data.minutes_dir,
          job_id: result.result.data.job_id
        }
      });

      // Store transcript data in localStorage for the transcript page
      const transcriptData = {
        transcript: result.result.data.transcript,
        speakers: result.result.data.speakers,
        title: title,
        meetingDate: meetingDate.toISOString(),
        attendance: facultyAttendance,
        timestamp: result.result.data.timestamp || new Date().toISOString(),
        fileName: result.result.data.file_name || result.file?.name || 'transcript',
        transcript_file: result.result.data.transcript_file,
        minutes_file: result.result.data.minutes_file,
        minutes: result.result.data.minutes,
        minute_id: result.result.data.minute_id, // Include the database minute ID
        isTextFile: isTextFile, // Add flag to indicate if this is a text file
        language: textLanguage, // Add the language of the text file
        textLanguage: textLanguage // Add the text language (for backward compatibility)
      };

      // Add original transcript data if available (for Tagalog text files)
      if (result.result.data.original_transcript) {
        transcriptData.original_transcript = result.result.data.original_transcript;
      }

      // Add original transcript file path if available
      if (result.result.data.original_transcript_file) {
        transcriptData.original_transcript_file = result.result.data.original_transcript_file;
      }

      // Check if minutes content is available and not empty
      if (!transcriptData.minutes || transcriptData.minutes.trim() === '') {
        console.log('Minutes content is empty or missing, setting placeholder to fetch from file...');

        // Store minutes_dir and job_id for path construction if needed
        transcriptData.minutes_dir = result.result.data.minutes_dir;
        transcriptData.job_id = result.result.data.job_id;

        // If we have a minutes file path, set a placeholder to trigger file fetch in Minutes.jsx
        if (transcriptData.minutes_file &&
            typeof transcriptData.minutes_file === 'string' &&
            transcriptData.minutes_file.trim() !== '') {

          console.log('Minutes file path available:', transcriptData.minutes_file);
          transcriptData.minutes = "FETCH_FROM_FILE";
        } else {
          console.warn('Missing or empty minutes file path, attempting to construct one');

          // Try to construct a valid path if we have the minutes directory
          if (transcriptData.minutes_dir &&
              typeof transcriptData.minutes_dir === 'string' &&
              transcriptData.minutes_dir.trim() !== '') {

            // Construct a path to the minutes.md file in the minutes directory
            const minutesFilePath = `${transcriptData.minutes_dir}/minutes.md`;
            console.log('Constructed minutes file path from directory:', minutesFilePath);

            transcriptData.minutes_file = minutesFilePath;
            transcriptData.minutes = "FETCH_FROM_FILE";
          } else if (transcriptData.job_id) {
            // Try to construct a path using the job ID
            const minutesFilePath = `uploads/processed/${transcriptData.job_id}/minutes of the meeting/minutes.md`;
            console.log('Constructed minutes file path from job ID:', minutesFilePath);

            transcriptData.minutes_file = minutesFilePath;
            transcriptData.minutes = "FETCH_FROM_FILE";
          } else {
            console.error('Cannot construct minutes file path, no directory or job ID available');

            // Set a placeholder message as minutes content
            transcriptData.minutes = "# Minutes Generation\n\nThe minutes file could not be located. Please try uploading your meeting recording again.";
          }
        }
      } else {
        console.log('Minutes content available in result data');

        // Even if we have minutes content, still store the file path for future reference
        if (transcriptData.minutes_file) {
          console.log('Storing minutes file path for future reference:', transcriptData.minutes_file);
        }
      }

      // Save transcript with timestamps to both currentTranscript and savedTranscripts
      const savedTranscript = saveTranscript(transcriptData, true);

      // Log the minute ID if available
      if (result.result.data.minute_id) {
        console.log(`Meeting saved to database with ID: ${result.result.data.minute_id}`);
      } else {
        console.log('Meeting processed but no database ID was returned');
      }

      // Also store the attendance data separately for easier access
      const attendanceData = {
        meetingId: result.jobId,
        meetingTitle: title,
        timestamp: new Date().toISOString(),
        facultyAttendance: facultyAttendance,
        minute_id: result.result.data.minute_id // Include the database minute ID
      };

      // Get existing attendance records or initialize empty array
      const existingAttendance = JSON.parse(localStorage.getItem('facultyAttendanceRecords') || '[]');

      // Add new attendance record
      existingAttendance.push(attendanceData);

      // Save back to localStorage
      localStorage.setItem('facultyAttendanceRecords', JSON.stringify(existingAttendance));

      console.log('Attendance data saved to localStorage:', attendanceData);

      // Navigate to the record-saved page after a short delay
      setTimeout(() => {
        navigate('/record-saved');
      }, 1000);

    } catch (error) {
      console.error('Upload error:', error);
      setError(`Upload failed: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="page-container" style={{
      display: 'flex',
      minHeight: '100vh',
      height: 'auto',
      fontFamily: "'Montserrat', sans-serif",
      overflow: 'visible',
      position: 'relative',
    }}>
      {/* Background Elements */}
      <div className="background-pattern" style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `
          linear-gradient(45deg, rgba(3, 79, 175, 0.1), rgba(87, 215, 226, 0.1)),
          url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23034FAF' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
        `,
        animation: 'backgroundShift 30s linear infinite',
        zIndex: 0,
      }} />

      {/* Gradient Overlay */}
      <div className="gradient-overlay" style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(3, 79, 175, 0.25) 100%)',
        zIndex: 1,
      }} />

      {/* Decorative Elements */}
      <div className="decorative-circle circle-1" style={{
        position: 'absolute',
        width: '400px',
        height: '400px',
        borderRadius: '50%',
        background: 'radial-gradient(circle, rgba(3, 79, 175, 0.25) 0%, rgba(87, 215, 226, 0.12) 70%)',
        top: '-150px',
        right: '5%',
        animation: 'float 15s ease-in-out infinite, shimmer 8s infinite',
        zIndex: 1,
        opacity: 0.7,
        filter: 'blur(80px)',
      }} />

      <div className="decorative-circle circle-2" style={{
        position: 'absolute',
        width: '300px',
        height: '300px',
        borderRadius: '50%',
        background: 'radial-gradient(circle, rgba(87, 215, 226, 0.25) 0%, rgba(3, 79, 175, 0.12) 70%)',
        bottom: '5%',
        left: '25%',
        animation: 'float 12s ease-in-out infinite reverse, shimmer 10s infinite 2s',
        zIndex: 1,
        opacity: 0.7,
        filter: 'blur(80px)',
      }} />

      {/* Main Content */}
      <Sidebar style={{ position: 'relative', zIndex: 2 }} />

      <div style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        padding: '30px',
        position: 'relative',
        zIndex: 2,
        height: 'auto',
        minHeight: '100vh',
        overflowY: 'visible',
        msOverflowStyle: 'auto', /* IE and Edge */
        scrollbarWidth: 'auto', /* Firefox */
      }}>
        {/* Custom scrollbar styles for WebKit browsers */}
        <style jsx>{`
          div::-webkit-scrollbar {
            width: 8px;
            display: block;
          }
          div::-webkit-scrollbar-track {
            background: rgba(3, 79, 175, 0.05);
            border-radius: 4px;
          }
          div::-webkit-scrollbar-thumb {
            background: rgba(3, 79, 175, 0.2);
            border-radius: 4px;
          }
          div::-webkit-scrollbar-thumb:hover {
            background: rgba(3, 79, 175, 0.4);
          }
        `}</style>
        {/* Header Section */}
        <div style={{
          background: 'rgba(255, 255, 255, 0.15)',
          padding: '20px 30px',
          borderRadius: '15px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
          marginBottom: '30px',
          display: 'flex',
          alignItems: 'center',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.25)',
          transition: 'all 0.3s ease',
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
            padding: '8px',
            borderRadius: '8px',
            transition: 'all 0.3s ease',
          }}
          onClick={() => navigate(-1)}
          onMouseOver={(e) => e.currentTarget.style.background = 'rgba(3, 79, 175, 0.1)'}
          onMouseOut={(e) => e.currentTarget.style.background = 'transparent'}>
            <img
              src={backIcon}
              alt="Back"
              style={{
                width: '24px',
                height: '24px',
                marginRight: '10px',
              }}
            />
            <span style={{ color: '#034FAF', fontWeight: '600', cursor: 'pointer' }}>Back</span>
          </div>
          <h1 style={{
            margin: '0 auto',
            fontSize: "24px",
            fontWeight: "900",
            color: '#034FAF',
            letterSpacing: '1px',
            cursor: 'default',
            textShadow: '0 1px 3px rgba(255, 255, 255, 0.7)',
          }}>
            UPLOAD MEETING FILES OR TRANSCRIPTS
          </h1>
        </div>

        {/* Main Content */}
        <div style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'flex-start',
          padding: '10px',
          paddingBottom: '30px', /* Added bottom padding for more space */
          height: 'auto',
          minHeight: 'calc(100vh - 150px)',
          overflowY: 'visible',
        }}>
          <div
            onClick={openFileDialog}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              background: 'rgba(255, 255, 255, 0.15)',
              padding: '40px',
              borderRadius: '20px',
              boxShadow: '0 4px 30px rgba(0, 0, 0, 0.1)',
              border: `2px dashed ${isDragging ? '#034FAF' : 'rgba(3, 79, 175, 0.2)'}`,
              backdropFilter: 'blur(10px)',
              WebkitBackdropFilter: 'blur(10px)',
              maxWidth: '800px',
              width: '100%',
              maxHeight: 'calc(100vh - 250px)',
              transition: 'all 0.3s ease',
              cursor: 'pointer',
              transform: isDragging ? 'scale(1.02)' : 'scale(1)',
              position: 'relative',
              animation: 'fadeIn 0.8s ease-out',
            }}
          >
            <input
              type="file"
              id="fileInput"
              onChange={handleFileChange}
              style={{ display: 'none' }}
              accept="video/*,audio/*,text/plain,text/markdown,.txt,.md"
            />

            <img
              src={uploadIcon}
              alt="Upload Icon"
              style={{
                height: '100px',
                marginBottom: '20px',
                transition: 'transform 0.3s ease',
                filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1))',
              }}
            />

            {error && (
              <div style={{
                color: '#ff4444',
                background: 'rgba(255, 68, 68, 0.1)',
                padding: '12px 24px',
                borderRadius: '8px',
                marginBottom: '20px',
                fontWeight: '500',
                width: '100%',
                textAlign: 'center',
              }}>
                {error}
              </div>
            )}

            <div style={{
              textAlign: 'center',
              color: '#666',
            }}>
              {file ? (
                <div style={{
                  background: 'rgba(3, 79, 175, 0.1)',
                  padding: '20px',
                  borderRadius: '12px',
                  width: '100%',
                }}>
                  <p style={{
                    margin: '0 0 10px 0',
                    color: '#034FAF',
                    fontWeight: '600',
                    fontSize: '16px',
                  }}>
                    {file.name}
                  </p>
                  <div style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: '10px',
                    marginBottom: '10px',
                  }}>
                    <p style={{
                      margin: 0,
                      color: '#666',
                      fontSize: '14px',
                    }}>
                      Size: {formatFileSize(file.size)}
                    </p>
                    {mediaDuration && (
                      <p style={{
                        margin: 0,
                        color: '#666',
                        fontSize: '14px',
                        display: 'flex',
                        alignItems: 'center',
                      }}>
                        <span style={{ marginRight: '5px' }}>•</span>
                        Duration: {formatDuration(mediaDuration)}
                      </p>
                    )}
                    {isTextFile && (
                      <div>
                        <p style={{
                          margin: 0,
                          color: '#034FAF',
                          fontSize: '14px',
                          display: 'flex',
                          alignItems: 'center',
                          fontWeight: '500',
                          marginBottom: '5px'
                        }}>
                          <span style={{ marginRight: '5px' }}>📄</span>
                          Text File
                        </p>
                      </div>
                    )}
                  </div>



                  {processingEstimate && (
                    <div style={{
                      marginTop: '15px',
                      padding: '10px',
                      background: 'rgba(3, 79, 175, 0.05)',
                      borderRadius: '8px',
                      border: '1px dashed rgba(3, 79, 175, 0.2)',
                    }}>
                      <p style={{
                        margin: 0,
                        color: '#034FAF',
                        fontSize: '14px',
                        fontWeight: '500',
                      }}>
                        <span style={{ marginRight: '5px' }}>⏱️</span>
                        Estimated processing time: {processingEstimate.description}
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <>
                  <p style={{
                    margin: '0 0 15px 0',
                    fontSize: '18px',
                    fontWeight: '600',
                    color: '#034FAF',
                  }}>
                    Drag and drop your file here
                  </p>
                  <p style={{
                    margin: '0 0 20px 0',
                    fontSize: '16px',
                  }}>
                    or click to choose file
                  </p>
                  <div style={{
                    background: 'rgba(3, 79, 175, 0.1)',
                    padding: '15px',
                    borderRadius: '12px',
                    fontSize: '14px',
                  }}>
                    <p style={{ margin: '0 0 10px 0', fontWeight: '600' }}>
                      Supported formats:
                    </p>
                    <p style={{ margin: '0 0 5px 0' }}>
                      Video: MP4, WebM, MKV, MOV, AVI
                    </p>
                    <p style={{ margin: '0 0 5px 0' }}>
                      Audio: MP3, WAV, M4A, FLAC, AAC, OGG
                    </p>
                    <p style={{ margin: '0 0 5px 0' }}>
                      Text: TXT, MD (transcripts)
                    </p>
                    <p style={{ margin: '0', color: '#034FAF' }}>
                      Maximum file size: 5GB
                    </p>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Text Language Selection - Outside the upload container */}
          {file && isTextFile && !isProcessing && !uploadResponse && (
            <div style={{
              marginTop: '20px',
              width: '100%',
              maxWidth: '800px',
              padding: '20px',
              background: 'rgba(255, 255, 255, 0.15)',
              borderRadius: '15px',
              boxShadow: '0 4px 15px rgba(0, 0, 0, 0.05)',
              backdropFilter: 'blur(10px)',
              WebkitBackdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.25)',
              animation: 'fadeIn 0.6s ease-out',
            }}>
              <h3 style={{
                color: '#034FAF',
                marginTop: 0,
                marginBottom: '15px',
                fontSize: '18px',
                fontWeight: '700',
                cursor: 'default',
              }}>Text Language</h3>

              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '15px',
              }}>
                <div style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: '15px',
                }}>
                  <label style={{
                    display: 'flex',
                    alignItems: 'center',
                    cursor: 'pointer',
                    padding: '12px 20px',
                    borderRadius: '8px',
                    background: textLanguage === 'tagalog' ? 'rgba(3, 79, 175, 0.1)' : 'rgba(255, 255, 255, 0.5)',
                    border: textLanguage === 'tagalog' ? '1px solid rgba(3, 79, 175, 0.5)' : '1px solid rgba(3, 79, 175, 0.1)',
                    transition: 'all 0.2s ease',
                    flex: '1 1 200px',
                  }}
                  onMouseOver={(e) => {
                    if (textLanguage !== 'tagalog') {
                      e.target.style.background = 'rgba(3, 79, 175, 0.05)';
                    }
                  }}
                  onMouseOut={(e) => {
                    if (textLanguage !== 'tagalog') {
                      e.target.style.background = 'rgba(255, 255, 255, 0.5)';
                    }
                  }}>
                    <input
                      type="radio"
                      name="textLanguage"
                      value="tagalog"
                      checked={textLanguage === 'tagalog'}
                      onChange={() => setTextLanguage('tagalog')}
                      style={{ marginRight: '10px' }}
                    />
                    <div>
                      <div style={{ fontWeight: '600', color: '#034FAF' }}>Tagalog</div>
                      <div style={{ fontSize: '13px', color: '#666', marginTop: '3px' }}>Needs translation to English</div>
                    </div>
                  </label>

                  <label style={{
                    display: 'flex',
                    alignItems: 'center',
                    cursor: 'pointer',
                    padding: '12px 20px',
                    borderRadius: '8px',
                    background: textLanguage === 'english' ? 'rgba(3, 79, 175, 0.1)' : 'rgba(255, 255, 255, 0.5)',
                    border: textLanguage === 'english' ? '1px solid rgba(3, 79, 175, 0.5)' : '1px solid rgba(3, 79, 175, 0.1)',
                    transition: 'all 0.2s ease',
                    flex: '1 1 200px',
                  }}
                  onMouseOver={(e) => {
                    if (textLanguage !== 'english') {
                      e.target.style.background = 'rgba(3, 79, 175, 0.05)';
                    }
                  }}
                  onMouseOut={(e) => {
                    if (textLanguage !== 'english') {
                      e.target.style.background = 'rgba(255, 255, 255, 0.5)';
                    }
                  }}>
                    <input
                      type="radio"
                      name="textLanguage"
                      value="english"
                      checked={textLanguage === 'english'}
                      onChange={() => setTextLanguage('english')}
                      style={{ marginRight: '10px' }}
                    />
                    <div>
                      <div style={{ fontWeight: '600', color: '#034FAF' }}>English</div>
                      <div style={{ fontSize: '13px', color: '#666', marginTop: '3px' }}>Skip translation process</div>
                    </div>
                  </label>
                </div>

                <div style={{
                  marginTop: '5px',
                  padding: '12px',
                  background: 'rgba(3, 79, 175, 0.05)',
                  borderRadius: '8px',
                  border: '1px dashed rgba(3, 79, 175, 0.2)',
                }}>
                  <p style={{
                    margin: 0,
                    fontSize: '14px',
                    color: '#666',
                    display: 'flex',
                    alignItems: 'center',
                  }}>
                    <span style={{ marginRight: '8px', fontSize: '16px' }}>ℹ️</span>
                    Text files will bypass audio processing and only perform translation if needed.
                  </p>
                </div>
              </div>
            </div>
          )}

          {isProcessing && (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              width: '100%'
            }}>
              <ProcessingIndicator
                progress={uploadProgress}
                stage={processingStage}
                status={processingStatus}
                file={file}
                details={processingDetails}
                duration={mediaDuration}
                estimatedTime={processingEstimate}
                iteration={processingIteration}
                totalIterations={processingTotalIterations}
              />
            </div>
          )}

          {uploadResponse && (
            <div style={{
              marginTop: '20px',
              padding: '15px',
              borderRadius: '8px',
              backgroundColor: 'rgba(0, 255, 0, 0.1)',
              border: '1px solid #00c853',
              color: '#00c853',
            }}>
              <h3>Upload Success!</h3>
              <pre style={{
                margin: '10px 0',
                padding: '10px',
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                borderRadius: '4px',
                overflow: 'auto',
                maxHeight: '200px'
              }}>
                {JSON.stringify(uploadResponse, null, 2)}
              </pre>
            </div>
          )}

          {/* Metadata Form */}
          {file && !isProcessing && !uploadResponse && (
            <div style={{
              marginTop: '20px',
              width: '100%',
              maxWidth: '800px',
              padding: '25px',
              background: 'rgba(255, 255, 255, 0.15)',
              borderRadius: '15px',
              boxShadow: '0 4px 15px rgba(0, 0, 0, 0.05)',
              height: 'auto',
              minHeight: '500px',
              overflowY: 'visible',
              backdropFilter: 'blur(10px)',
              WebkitBackdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.25)',
              animation: 'fadeIn 0.6s ease-out',
            }}>
              <h3 style={{
                color: '#034FAF',
                marginTop: 0,
                marginBottom: '20px',
                fontSize: '20px',
                fontWeight: '700',
                cursor: 'default',
                textShadow: '0 1px 3px rgba(255, 255, 255, 0.7)',
              }}>Meeting Information</h3>

              <div style={{ marginBottom: '20px' }}>
                <label
                  htmlFor="title"
                  style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontWeight: '600',
                    color: '#034FAF',
                    cursor: 'default',
                  }}
                >
                  Title <span style={{
                    fontSize: '14px',
                    color: '#666',
                    fontWeight: 'normal',
                    fontStyle: 'italic'
                  }}>(required)</span>
                </label>
                <input
                  id="title"
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter a title for this minute"
                  style={{
                    width: '100%',
                    padding: '12px',
                    borderRadius: '8px',
                    border: '1px solid rgba(3, 79, 175, 0.2)',
                    fontSize: '16px',
                    boxSizing: 'border-box',
                    background: 'rgba(255, 255, 255, 0.8)',
                    transition: 'all 0.3s ease',
                  }}
                  onFocus={(e) => {
                    e.target.style.background = 'white';
                    e.target.style.boxShadow = '0 0 0 2px rgba(3, 79, 175, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.target.style.background = 'rgba(255, 255, 255, 0.8)';
                    e.target.style.boxShadow = 'none';
                  }}
                  required
                />
              </div>

              <div style={{ marginBottom: '20px' }}>
                <DatePicker
                  selectedDate={meetingDate}
                  onChange={(date) => setMeetingDate(date)}
                  label="Meeting Date"
                />
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label
                  style={{
                    display: 'block',
                    marginBottom: '15px',
                    fontWeight: '600',
                    color: '#034FAF',
                    cursor: 'default',
                    fontSize: '18px',
                  }}
                >
                  Attendance
                </label>

                {/* Faculty Attendance Selector */}
                <div style={{
                  border: '1px solid rgba(3, 79, 175, 0.2)',
                  borderRadius: '8px',
                  padding: '15px',
                  backgroundColor: 'rgba(255, 255, 255, 0.7)',
                  minHeight: '300px',
                  height: 'auto',
                  overflowY: 'visible',
                }}>
                  <FacultyAttendanceSelector
                    onAttendanceChange={handleAttendanceChange}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '20px',
            marginTop: '30px',
            marginBottom: '50px', /* Added bottom margin to create space from the bottom of the page */
            width: '100%',
            maxWidth: '800px',
          }}>
            <button
              onClick={() => navigate('/home')}
              style={{
                padding: '15px 40px',
                background: 'rgba(255, 255, 255, 0.15)',
                color: '#034FAF',
                border: '2px solid #034FAF',
                borderRadius: '8px',
                fontWeight: '600',
                cursor: 'pointer',
                width: '200px',
                transition: 'all 0.3s ease',
                fontSize: '16px',
                boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',
                backdropFilter: 'blur(5px)',
                WebkitBackdropFilter: 'blur(5px)',
              }}
              onMouseOver={(e) => {
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.15)';
                e.target.style.background = 'rgba(3, 79, 175, 0.1)';
              }}
              onMouseOut={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.1)';
                e.target.style.background = 'rgba(255, 255, 255, 0.15)';
              }}
            >
              Cancel
            </button>
            <button
              onClick={() => handleUpload(file)}
              disabled={!file || error || isProcessing || !title.trim()}
              style={{
                padding: '15px 40px',
                background: file && !error && !isProcessing && title.trim()
                  ? 'linear-gradient(45deg, #034FAF, #0367d4)'
                  : 'rgba(204, 204, 204, 0.7)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontWeight: '600',
                cursor: file && !error && !isProcessing && title.trim() ? 'pointer' : 'not-allowed',
                width: '200px',
                transition: 'all 0.3s ease',
                fontSize: '16px',
                boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',
              }}
              onMouseOver={(e) => {
                if (file && !error && !isProcessing && title.trim()) {
                  e.target.style.transform = 'translateY(-2px)';
                  e.target.style.boxShadow = '0 6px 20px rgba(3, 79, 175, 0.3)';
                }
              }}
              onMouseOut={(e) => {
                if (file && !error && !isProcessing && title.trim()) {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.1)';
                }
              }}
            >
              {isProcessing ? 'Processing...' : 'Upload File'}
            </button>
          </div>
        </div>
      </div>

      {/* Add animation keyframes and hide scrollbars */}
      <style>
        {`
          @keyframes backgroundShift {
            0% { background-position: 0 0; }
            100% { background-position: 100% 100%; }
          }

          @keyframes float {
            0% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(5deg); }
            100% { transform: translateY(0) rotate(0deg); }
          }

          @keyframes shimmer {
            0% { opacity: 0.7; }
            50% { opacity: 0.9; }
            100% { opacity: 0.7; }
          }

          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
          }

          /* Default scrollbar styles */
          *::-webkit-scrollbar {
            width: 8px;
          }
          *::-webkit-scrollbar-track {
            background: rgba(3, 79, 175, 0.05);
          }
          *::-webkit-scrollbar-thumb {
            background: rgba(3, 79, 175, 0.2);
            border-radius: 4px;
          }
          *::-webkit-scrollbar-thumb:hover {
            background: rgba(3, 79, 175, 0.4);
          }

          /* Default cursor for text elements */
          h1, h2, h3, h4, h5, h6, p, span, div, section, label {
            cursor: default !important;
          }

          /* Ensure buttons and interactive elements have pointer cursor */
          button, a, .clickable, [role="button"], input[type="file"], input[type="submit"] {
            cursor: pointer !important;
          }
        `}
      </style>
    </div>
  );
};

export default UploadRecorded;
